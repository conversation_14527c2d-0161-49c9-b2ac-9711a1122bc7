<% layout('layout') -%>

<!-- Header -->
<div class="bg-dark-800 border-b border-gray-700 p-6 -mx-6 -mt-6 mb-6">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-white">Program Referral</h1>
      <p class="text-gray-400 mt-1">Ajak teman dan dapatkan komisi 5% dari setiap pembelian plan</p>
    </div>
    <div class="flex items-center space-x-4">
      <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
        <i class="ti ti-gift text-white text-sm"></i>
      </div>
    </div>
  </div>
</div>

<!-- Referral Info Alert -->
<div class="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 mb-6">
  <div class="flex items-start space-x-3">
    <i class="ti ti-info-circle text-blue-400 text-xl mt-0.5"></i>
    <div>
      <h3 class="text-blue-400 font-medium mb-1">Cara Kerja Program Referral</h3>
      <p class="text-gray-300 text-sm">
        Dengan mengaktifkan program referral, Anda akan mendapatkan kode unik yang dapat dibagikan kepada teman-teman Anda. 
        Dapatkan komisi <strong>5%</strong> untuk setiap teman yang berlangganan menggunakan kode referral Anda.
      </p>
    </div>
  </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
  <!-- Balance -->
  <div class="card-enhanced p-6">
    <div class="flex items-center justify-between mb-4">
      <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-wallet text-green-400 text-xl"></i>
      </div>
      <span class="text-sm text-gray-400">Saldo</span>
    </div>
    <div class="text-2xl font-bold text-white mb-1">
      <%= formatIDR(stats.balance) %>
    </div>
    <p class="text-gray-400 text-sm">Saldo tersedia</p>
  </div>

  <!-- Clicks -->
  <div class="card-enhanced p-6">
    <div class="flex items-center justify-between mb-4">
      <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-click text-blue-400 text-xl"></i>
      </div>
      <span class="text-sm text-gray-400">Clicks</span>
    </div>
    <div class="text-2xl font-bold text-white mb-1">
      <%= stats.total_clicks %>
    </div>
    <p class="text-gray-400 text-sm">Total klik link</p>
  </div>

  <!-- Pending -->
  <div class="card-enhanced p-6">
    <div class="flex items-center justify-between mb-4">
      <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-clock text-yellow-400 text-xl"></i>
      </div>
      <span class="text-sm text-gray-400">Pending</span>
    </div>
    <div class="text-2xl font-bold text-white mb-1">
      <%= stats.pending_referrals %>
    </div>
    <p class="text-gray-400 text-sm">Referral pending</p>
  </div>

  <!-- Success -->
  <div class="card-enhanced p-6">
    <div class="flex items-center justify-between mb-4">
      <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-check text-green-400 text-xl"></i>
      </div>
      <span class="text-sm text-gray-400">Success</span>
    </div>
    <div class="text-2xl font-bold text-white mb-1 text-green-400">
      <%= stats.successful_referrals %>
    </div>
    <p class="text-gray-400 text-sm">Referral berhasil</p>
  </div>
</div>

<!-- Referral Link Section -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
  <!-- Referral Link -->
  <div class="card-enhanced p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-white">Referral Link</h3>
      <button onclick="copyReferralLink()" class="btn-secondary text-sm">
        <i class="ti ti-copy mr-2"></i>
        Salin Link
      </button>
    </div>
    
    <div class="bg-dark-700 rounded-lg p-4 mb-4">
      <p class="text-gray-400 text-sm mb-2">Kode Referral Anda:</p>
      <div class="flex items-center space-x-2">
        <code class="text-primary font-mono text-lg"><%= stats.referral_code %></code>
        <button onclick="copyReferralCode()" class="text-gray-400 hover:text-white">
          <i class="ti ti-copy"></i>
        </button>
      </div>
    </div>

    <div class="bg-dark-700 rounded-lg p-4">
      <p class="text-gray-400 text-sm mb-2">Link Referral:</p>
      <div class="flex items-center space-x-2">
        <input 
          type="text" 
          id="referralLink" 
          value="<%= referralLink %>" 
          readonly 
          class="flex-1 bg-transparent text-white text-sm font-mono border-none outline-none"
        >
        <button onclick="copyReferralLink()" class="text-gray-400 hover:text-white">
          <i class="ti ti-copy"></i>
        </button>
      </div>
    </div>

    <p class="text-gray-400 text-sm mt-4">
      Bagikan link ini kepada teman-teman Anda untuk mendapatkan komisi ketika mereka berlangganan.
    </p>
  </div>

  <!-- Withdrawal Section -->
  <div class="card-enhanced p-6">
    <h3 class="text-lg font-semibold text-white mb-4">Tarik Saldo</h3>
    
    <div class="mb-4">
      <p class="text-gray-400 text-sm mb-2">Saldo Tersedia:</p>
      <p class="text-2xl font-bold text-green-400"><%= formatIDR(stats.balance) %></p>
    </div>

    <% if (stats.balance >= 50000) { %>
      <button onclick="openWithdrawModal()" class="btn-primary w-full">
        <i class="ti ti-cash mr-2"></i>
        Tarik Saldo
      </button>
    <% } else { %>
      <div class="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4">
        <p class="text-yellow-400 text-sm">
          <i class="ti ti-info-circle mr-2"></i>
          Minimum penarikan adalah Rp 50.000
        </p>
      </div>
    <% } %>

    <p class="text-gray-400 text-xs mt-4">
      Penarikan akan diproses secara manual oleh admin dalam 1-3 hari kerja.
    </p>
  </div>
</div>

<!-- Earnings History -->
<div class="card-enhanced p-6 mb-8">
  <div class="flex items-center justify-between mb-6">
    <h3 class="text-lg font-semibold text-white">Riwayat Komisi</h3>
    <button onclick="loadEarningsHistory()" class="btn-secondary text-sm">
      <i class="ti ti-refresh mr-2"></i>
      Refresh
    </button>
  </div>

  <div id="earningsHistory">
    <% if (earnings && earnings.length > 0) { %>
      <div class="space-y-4">
        <% earnings.forEach(earning => { %>
          <div class="bg-dark-700 rounded-lg p-4 flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                <i class="ti ti-coins text-green-400"></i>
              </div>
              <div>
                <p class="text-white font-medium">Komisi dari <%= earning.referee_username || 'User' %></p>
                <p class="text-gray-400 text-sm">
                  Kode: <%= earning.referral_code %> • 
                  <%= new Date(earning.created_at).toLocaleDateString('id-ID') %>
                </p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-green-400 font-semibold">+<%= formatIDR(earning.amount) %></p>
              <p class="text-gray-400 text-sm capitalize"><%= earning.status %></p>
            </div>
          </div>
        <% }); %>
      </div>
    <% } else { %>
      <div class="text-center py-8">
        <i class="ti ti-coins text-gray-600 text-4xl mb-4"></i>
        <p class="text-gray-400">Belum ada komisi yang diterima</p>
        <p class="text-gray-500 text-sm mt-2">Bagikan link referral Anda untuk mulai mendapatkan komisi</p>
      </div>
    <% } %>
  </div>
</div>

<!-- Referral Details -->
<div class="mt-8 mb-8">
  <div class="flex flex-col lg:flex-row lg:space-x-6 space-y-6 lg:space-y-0">
    <!-- Pending Referrals -->
    <div class="flex-1 card-enhanced referral-card p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-white">Referral Pending</h3>
        <button onclick="loadReferralDetails()" class="btn-secondary text-sm px-3 py-1.5">
          <i class="ti ti-refresh mr-1"></i>
          Refresh
        </button>
      </div>

      <div id="pendingReferrals" class="min-h-[200px]">
        <div class="text-center py-12 animate-pulse-slow">
          <i class="ti ti-loader-2 text-gray-600 text-3xl mb-3 animate-spin"></i>
          <p class="text-gray-400 text-sm">Memuat data...</p>
        </div>
      </div>
    </div>

    <!-- Successful Referrals -->
    <div class="flex-1 card-enhanced referral-card p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-white">Referral Berhasil</h3>
        <button onclick="loadReferralDetails()" class="btn-secondary text-sm px-3 py-1.5">
          <i class="ti ti-refresh mr-1"></i>
          Refresh
        </button>
      </div>

      <div id="successfulReferrals" class="min-h-[200px]">
        <div class="text-center py-12 animate-pulse-slow">
          <i class="ti ti-loader-2 text-gray-600 text-3xl mb-3 animate-spin"></i>
          <p class="text-gray-400 text-sm">Memuat data...</p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Withdrawal Modal -->
<div id="withdrawModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-dark-800 rounded-lg p-6 w-full max-w-md">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-white">Tarik Saldo</h3>
        <button onclick="closeWithdrawModal()" class="text-gray-400 hover:text-white">
          <i class="ti ti-x text-xl"></i>
        </button>
      </div>

      <form id="withdrawForm" onsubmit="submitWithdrawal(event)">
        <div class="space-y-4">
          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Jumlah Penarikan</label>
            <input 
              type="number" 
              name="amount" 
              min="50000" 
              max="<%= stats.balance %>"
              step="1000"
              required
              class="w-full bg-dark-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-primary focus:outline-none"
              placeholder="Minimum Rp 50.000"
            >
          </div>

          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Nama Bank</label>
            <input 
              type="text" 
              name="bankName" 
              required
              class="w-full bg-dark-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-primary focus:outline-none"
              placeholder="Contoh: Bank BCA"
            >
          </div>

          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Nomor Rekening</label>
            <input 
              type="text" 
              name="accountNumber" 
              required
              class="w-full bg-dark-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-primary focus:outline-none"
              placeholder="Nomor rekening bank"
            >
          </div>

          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Nama Pemilik Rekening</label>
            <input 
              type="text" 
              name="accountName" 
              required
              class="w-full bg-dark-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-primary focus:outline-none"
              placeholder="Nama sesuai rekening bank"
            >
          </div>
        </div>

        <div class="flex space-x-3 mt-6">
          <button type="button" onclick="closeWithdrawModal()" class="flex-1 btn-secondary">
            Batal
          </button>
          <button type="submit" class="flex-1 btn-primary">
            <i class="ti ti-cash mr-2"></i>
            Ajukan Penarikan
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
function copyReferralCode() {
  const code = '<%= stats.referral_code %>';
  navigator.clipboard.writeText(code).then(() => {
    showNotification('Kode referral berhasil disalin!', 'success');
  });
}

function copyReferralLink() {
  const link = document.getElementById('referralLink').value;
  navigator.clipboard.writeText(link).then(() => {
    showNotification('Link referral berhasil disalin!', 'success');
  });
}

function openWithdrawModal() {
  document.getElementById('withdrawModal').classList.remove('hidden');
}

function closeWithdrawModal() {
  document.getElementById('withdrawModal').classList.add('hidden');
  document.getElementById('withdrawForm').reset();
}

async function submitWithdrawal(event) {
  event.preventDefault();
  
  const formData = new FormData(event.target);
  const data = {
    amount: formData.get('amount'),
    bankName: formData.get('bankName'),
    accountNumber: formData.get('accountNumber'),
    accountName: formData.get('accountName')
  };

  try {
    const response = await fetch('/referral/api/withdraw', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': '<%= csrfToken %>'
      },
      body: JSON.stringify(data)
    });

    const result = await response.json();

    if (result.success) {
      showNotification('Permintaan penarikan berhasil diajukan!', 'success');
      closeWithdrawModal();
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } else {
      showNotification(result.error || 'Gagal mengajukan penarikan', 'error');
    }
  } catch (error) {
    console.error('Withdrawal error:', error);
    showNotification('Terjadi kesalahan saat mengajukan penarikan', 'error');
  }
}

async function loadEarningsHistory() {
  try {
    const response = await fetch('/referral/api/earnings');
    const result = await response.json();

    if (result.success) {
      // Update earnings history display
      // Implementation depends on your notification system
      showNotification('Riwayat komisi berhasil diperbarui', 'success');
    }
  } catch (error) {
    console.error('Load earnings error:', error);
    showNotification('Gagal memuat riwayat komisi', 'error');
  }
}

async function loadReferralDetails() {
  try {
    // Show loading state
    const pendingContainer = document.getElementById('pendingReferrals');
    const successfulContainer = document.getElementById('successfulReferrals');

    pendingContainer.innerHTML = `
      <div class="text-center py-12 animate-pulse-slow">
        <i class="ti ti-loader-2 text-gray-600 text-3xl mb-3 animate-spin"></i>
        <p class="text-gray-400 text-sm">Memuat data...</p>
      </div>
    `;

    successfulContainer.innerHTML = `
      <div class="text-center py-12 animate-pulse-slow">
        <i class="ti ti-loader-2 text-gray-600 text-3xl mb-3 animate-spin"></i>
        <p class="text-gray-400 text-sm">Memuat data...</p>
      </div>
    `;

    const response = await fetch('/referral/api/referral-details');
    const result = await response.json();

    if (result.success) {
      const { pending, successful } = result.data;

      // Update pending referrals
      const pendingContainer = document.getElementById('pendingReferrals');
      if (pending.length > 0) {
        pendingContainer.innerHTML = `
          <div class="space-y-2 max-h-60 overflow-y-auto pr-2">
            ${pending.map(ref => `
              <div class="bg-dark-700/50 rounded-lg p-3 border border-gray-700/50 hover:border-yellow-500/30 transition-colors">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                      <i class="ti ti-clock text-yellow-400 text-sm"></i>
                    </div>
                    <div class="min-w-0">
                      <p class="text-white font-medium text-sm truncate">${ref.masked_username}</p>
                      <p class="text-gray-400 text-xs">
                        ${new Date(ref.signup_date).toLocaleDateString('id-ID')}
                      </p>
                    </div>
                  </div>
                  <div class="text-right flex-shrink-0 ml-2">
                    <p class="text-yellow-400 text-xs font-medium">Belum beli plan</p>
                  </div>
                </div>
              </div>
            `).join('')}
          </div>
        `;
      } else {
        pendingContainer.innerHTML = `
          <div class="text-center py-12">
            <i class="ti ti-clock text-gray-600 text-3xl mb-3"></i>
            <p class="text-gray-400 text-sm">Tidak ada referral pending</p>
            <p class="text-gray-500 text-xs mt-1">Bagikan link referral untuk mendapatkan lebih banyak signup</p>
          </div>
        `;
      }

      // Update successful referrals
      const successfulContainer = document.getElementById('successfulReferrals');
      if (successful.length > 0) {
        successfulContainer.innerHTML = `
          <div class="space-y-2 max-h-60 overflow-y-auto pr-2">
            ${successful.map(ref => `
              <div class="bg-dark-700/50 rounded-lg p-3 border border-gray-700/50 hover:border-green-500/30 transition-colors">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                      <i class="ti ti-check text-green-400 text-sm"></i>
                    </div>
                    <div class="min-w-0">
                      <p class="text-white font-medium text-sm truncate">${ref.masked_username}</p>
                      <p class="text-gray-400 text-xs">
                        ${new Date(ref.completed_date).toLocaleDateString('id-ID')}
                      </p>
                    </div>
                  </div>
                  <div class="text-right flex-shrink-0 ml-2">
                    <p class="text-green-400 text-xs font-semibold">+${formatIDRClient(ref.commission_amount)}</p>
                  </div>
                </div>
              </div>
            `).join('')}
          </div>
        `;
      } else {
        successfulContainer.innerHTML = `
          <div class="text-center py-12">
            <i class="ti ti-check text-gray-600 text-3xl mb-3"></i>
            <p class="text-gray-400 text-sm">Belum ada referral berhasil</p>
            <p class="text-gray-500 text-xs mt-1">Tunggu hingga referral Anda membeli plan</p>
          </div>
        `;
      }
    }
  } catch (error) {
    console.error('Load referral details error:', error);
    showNotification('Gagal memuat detail referral', 'error');
  }
}

function formatIDRClient(amount) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0
  }).format(amount);
}

function showNotification(message, type) {
  // Implementation depends on your existing notification system
  // For now, use simple alert
  alert(message);
}

// Load referral details when page loads
document.addEventListener('DOMContentLoaded', function() {
  loadReferralDetails();
});
</script>

<style>
/* Custom scrollbar for referral lists */
.max-h-60::-webkit-scrollbar {
  width: 4px;
}

.max-h-60::-webkit-scrollbar-track {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 2px;
}

.max-h-60::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.max-h-60::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* Ensure equal height cards */
.referral-card {
  min-height: 280px;
}

/* Better spacing for mobile */
@media (max-width: 1024px) {
  .card-enhanced {
    padding: 1rem;
  }

  .flex.lg\\:space-x-6 {
    gap: 1.5rem;
  }
}

/* Ensure proper spacing on desktop */
@media (min-width: 1024px) {
  .flex.lg\\:space-x-6 {
    gap: 1.5rem;
  }
}

/* Loading animation */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
