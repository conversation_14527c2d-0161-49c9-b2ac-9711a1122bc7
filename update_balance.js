const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Path to database
const dbPath = path.join(__dirname, 'db', 'streamonpod.db');
const db = new sqlite3.Database(dbPath);

console.log('🔄 Updating referral balance for user: kimdogi');

// Update saldo referral untuk user kimdogi
db.run(
  'UPDATE users SET referral_balance = ? WHERE username = ?',
  [50000, 'kimdogi'],
  function(err) {
    if (err) {
      console.error('❌ Error updating balance:', err);
      db.close();
      return;
    }
    
    console.log('✅ Updated referral balance for kimdogi');
    console.log('📊 Rows affected:', this.changes);
    
    if (this.changes === 0) {
      console.log('⚠️ No rows were updated. User might not exist.');
    }
    
    // Verify the update
    db.get(
      'SELECT username, referral_balance FROM users WHERE username = ?',
      ['kimdogi'],
      (err, row) => {
        if (err) {
          console.error('❌ Error verifying update:', err);
        } else if (row) {
          console.log('✅ Verification successful:');
          console.log('   Username:', row.username);
          console.log('   Balance: Rp', row.referral_balance.toLocaleString('id-ID'));
        } else {
          console.log('❌ User kimdogi not found in database');
          
          // Show available users for debugging
          db.all('SELECT username FROM users LIMIT 5', (err, users) => {
            if (!err && users.length > 0) {
              console.log('📋 Available users (first 5):');
              users.forEach(user => console.log('   -', user.username));
            }
          });
        }
        
        db.close();
        console.log('🔚 Database connection closed');
      }
    );
  }
);
