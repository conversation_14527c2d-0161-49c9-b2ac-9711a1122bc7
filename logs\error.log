2025-06-02T03:15:28.648Z [ERROR] Error getting video info: Error: Command failed with exit code 1: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffprobe-installer\win32-x64\ffprobe.exe -v error -show_format -show_streams C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\perancis-aniversaire-1748834116166-431421.mkv
C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\perancis-aniversaire-1748834116166-431421.mkv: No such file or directory
    at makeError (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\execa\lib\error.js:60:11)
    at handlePromise (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\execa\index.js:118:26)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5) {
  shortMessage: 'Command failed with exit code 1: C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe -v error -show_format -show_streams C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834116166-431421.mkv',
  command: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe -v error -show_format -show_streams C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834116166-431421.mkv',
  escapedCommand: '"C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe" -v error -show_format -show_streams "C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834116166-431421.mkv"',
  exitCode: 1,
  signal: undefined,
  signalDescription: undefined,
  stdout: '',
  stderr: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834116166-431421.mkv: No such file or directory',
  failed: true,
  timedOut: false,
  isCanceled: false,
  killed: false
}
2025-06-02T03:15:28.649Z [ERROR] Error processing Google Drive import: Error: Command failed with exit code 1: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffprobe-installer\win32-x64\ffprobe.exe -v error -show_format -show_streams C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\perancis-aniversaire-1748834116166-431421.mkv
C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\perancis-aniversaire-1748834116166-431421.mkv: No such file or directory
    at makeError (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\execa\lib\error.js:60:11)
    at handlePromise (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\execa\index.js:118:26)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5) {
  shortMessage: 'Command failed with exit code 1: C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe -v error -show_format -show_streams C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834116166-431421.mkv',
  command: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe -v error -show_format -show_streams C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834116166-431421.mkv',
  escapedCommand: '"C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe" -v error -show_format -show_streams "C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834116166-431421.mkv"',
  exitCode: 1,
  signal: undefined,
  signalDescription: undefined,
  stdout: '',
  stderr: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834116166-431421.mkv: No such file or directory',
  failed: true,
  timedOut: false,
  isCanceled: false,
  killed: false
}
2025-06-02T03:20:06.125Z [ERROR] Error getting video info: Error: Command failed with exit code 1: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffprobe-installer\win32-x64\ffprobe.exe -v error -show_format -show_streams C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\perancis-aniversaire-1748834397920-436703.mkv
C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\perancis-aniversaire-1748834397920-436703.mkv: No such file or directory
    at makeError (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\execa\lib\error.js:60:11)
    at handlePromise (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\execa\index.js:118:26)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5) {
  shortMessage: 'Command failed with exit code 1: C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe -v error -show_format -show_streams C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834397920-436703.mkv',
  command: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe -v error -show_format -show_streams C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834397920-436703.mkv',
  escapedCommand: '"C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe" -v error -show_format -show_streams "C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834397920-436703.mkv"',
  exitCode: 1,
  signal: undefined,
  signalDescription: undefined,
  stdout: '',
  stderr: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834397920-436703.mkv: No such file or directory',
  failed: true,
  timedOut: false,
  isCanceled: false,
  killed: false
}
2025-06-02T03:20:06.127Z [ERROR] Error processing Google Drive import: Error: Command failed with exit code 1: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffprobe-installer\win32-x64\ffprobe.exe -v error -show_format -show_streams C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\perancis-aniversaire-1748834397920-436703.mkv
C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\perancis-aniversaire-1748834397920-436703.mkv: No such file or directory
    at makeError (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\execa\lib\error.js:60:11)
    at handlePromise (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\execa\index.js:118:26)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5) {
  shortMessage: 'Command failed with exit code 1: C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe -v error -show_format -show_streams C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834397920-436703.mkv',
  command: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe -v error -show_format -show_streams C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834397920-436703.mkv',
  escapedCommand: '"C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe" -v error -show_format -show_streams "C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834397920-436703.mkv"',
  exitCode: 1,
  signal: undefined,
  signalDescription: undefined,
  stdout: '',
  stderr: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834397920-436703.mkv: No such file or directory',
  failed: true,
  timedOut: false,
  isCanceled: false,
  killed: false,
  localFilePath: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834397920-436703.mkv'
}
2025-06-02T03:36:45.078Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:36:45.084Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:36:48.151Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list:
2025-06-02T03:36:48.156Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:36:51.215Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list:
2025-06-02T03:36:51.220Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:36:54.292Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:36:54.297Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:36:57.367Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:36:57.371Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:37:17.490Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:37:17.500Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:37:20.569Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:37:20.573Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:37:23.630Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:37:23.635Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:37:26.702Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:37:26.709Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:37:29.780Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list:
2025-06-02T03:37:29.784Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:38:15.738Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list:
2025-06-02T03:38:15.742Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:38:18.809Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:38:18.813Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:38:21.905Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:38:21.913Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:38:24.975Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:38:24.979Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:38:28.055Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:38:28.060Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:07:20.768Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:07:24.345Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:07:27.924Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:07:31.498Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:07:35.073Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:08:05.687Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:08:09.275Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:08:12.975Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:08:16.553Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:08:20.442Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:34:47.619Z [ERROR] -----------------------------------
2025-06-02T04:34:47.619Z [ERROR] UNCAUGHT EXCEPTION: Error: listen EADDRINUSE: address already in use 0.0.0.0:7575
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at doListen (node:net:2069:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '0.0.0.0',
  port: 7575
}
2025-06-02T04:34:47.621Z [ERROR] -----------------------------------
2025-06-02T04:40:09.898Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2331:45
2025-06-02T04:40:16.748Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2331:45
2025-06-02T04:41:41.965Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2331:45
2025-06-02T04:41:49.325Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2331:45
2025-06-02T04:42:51.039Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2331:45
2025-06-02T04:45:10.504Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2331:45
2025-06-02T04:46:24.707Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2347:45
2025-06-02T04:46:31.328Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2347:45
2025-06-02T04:47:01.251Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2347:45
2025-06-02T04:47:04.764Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2347:45
2025-06-02T04:49:05.766Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2347:45
2025-06-02T04:49:05.767Z [ERROR] Error details: {
  message: "Unexpected token ':'",
  stack: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\utils\\googleDriveService.js:60\n' +
    '      mimeType: fileMetadata.data.mimeType,\n' +
    '              ^\n' +
    '\n' +
    "SyntaxError: Unexpected token ':'\n" +
    '    at internalCompileFunction (node:internal/vm:77:18)\n' +
    '    at wrapSafe (node:internal/modules/cjs/loader:1288:20)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1340:27)\n' +
    '    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1207:32)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1023:12)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1235:19)\n' +
    '    at require (node:internal/modules/helpers:176:18)\n' +
    '    at C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\app.js:2347:45',
  userId: '3e42578a-2600-4584-907f-53af5c4853c6',
  sessionExists: true
}
2025-06-02T04:51:18.279Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2351:45
2025-06-02T04:51:18.280Z [ERROR] Error details: {
  message: "Unexpected token ':'",
  stack: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\utils\\googleDriveService.js:60\n' +
    '      mimeType: fileMetadata.data.mimeType,\n' +
    '              ^\n' +
    '\n' +
    "SyntaxError: Unexpected token ':'\n" +
    '    at internalCompileFunction (node:internal/vm:77:18)\n' +
    '    at wrapSafe (node:internal/modules/cjs/loader:1288:20)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1340:27)\n' +
    '    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1207:32)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1023:12)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1235:19)\n' +
    '    at require (node:internal/modules/helpers:176:18)\n' +
    '    at C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\app.js:2351:45',
  userId: '3e42578a-2600-4584-907f-53af5c4853c6',
  sessionExists: true
}
2025-06-02T04:53:55.822Z [ERROR] Error downloading file from Google Drive: Error: Only MP4 and MOV video files are supported for optimal performance. File type: video/x-matroska, Name: Perancis-Aniversaire.mkv
    at downloadFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:73:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2405:20)
2025-06-02T04:53:55.823Z [ERROR] Error processing Google Drive import: Error: Only MP4 and MOV video files are supported for optimal performance. File type: video/x-matroska, Name: Perancis-Aniversaire.mkv
    at downloadFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:73:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2405:20)
2025-06-02T04:54:55.351Z [ERROR] Error downloading file from Google Drive: Error: Only MP4 and MOV video files are supported for optimal performance. File type: video/x-matroska, Name: Perancis-Aniversaire.mkv
    at downloadFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:73:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2405:20)
2025-06-02T04:54:55.352Z [ERROR] Error processing Google Drive import: Error: Only MP4 and MOV video files are supported for optimal performance. File type: video/x-matroska, Name: Perancis-Aniversaire.mkv
    at downloadFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:73:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2405:20)
2025-06-02T05:10:20.471Z [ERROR] Error downloading file from Google Drive: Error: Only MP4 and MOV video files are supported for optimal performance. File type: image/jpeg, Name: 20240425_151212.jpg
    at downloadFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:73:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2404:20)
2025-06-02T05:10:20.471Z [ERROR] Error processing Google Drive import: Error: Only MP4 and MOV video files are supported for optimal performance. File type: image/jpeg, Name: 20240425_151212.jpg
    at downloadFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:73:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2404:20)
2025-06-02T05:12:07.216Z [ERROR] ⚠️  High memory usage: 94.78%
2025-06-02T05:19:50.923Z [ERROR] Error processing Google Drive import: TypeError: Cannot read properties of undefined (reading 'toFixed')
    at processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2429:125)
2025-06-02T05:20:14.065Z [ERROR] Error processing Google Drive import: TypeError: Cannot read properties of undefined (reading 'toFixed')
    at processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2429:125)
2025-06-02T05:22:20.079Z [ERROR] Google Drive URL parsing error: Error: Invalid Google Drive URL format
    at extractFileId (C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:28:9)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2352:22
2025-06-02T05:22:28.655Z [ERROR] Error processing Google Drive import: Error: Storage quota exceeded. File size: 0.02GB, Available: -0.00GB (0.02GB used of 0.01GB). Please upgrade your plan or free up space.
    at processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2432:15)
2025-06-02T05:23:16.346Z [ERROR] Error processing Google Drive import: Error: Storage quota exceeded. File size: 0.02GB, Available: 0.01GB (0.00GB used of 0.01GB). Please upgrade your plan or free up space.
    at processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2432:15)
2025-06-02T05:37:15.301Z [ERROR] Google Drive URL parsing error: Error: Invalid Google Drive URL format
    at extractFileId (C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:28:9)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2372:22
2025-06-02T05:38:37.465Z [ERROR] Error processing Google Drive import: Error: Storage quota exceeded. File size: 0.01GB, Available: 0.01GB (0.01GB used of 0.01GB). Please upgrade your plan or free up space.
    at processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2452:15)
2025-06-02T07:46:56.847Z [ERROR] Error: Upload ID and chunk index are required
    at DiskStorage.filename [as getFilename] (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:28:17)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\storage\disk.js:34:10
    at DiskStorage.destination [as getDestination] (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:22:5)
    at DiskStorage._handleFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\storage\disk.js:31:8)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\lib\make-middleware.js:137:17
    at chunkFilter (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:48:3)
    at wrappedFileFilter (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\index.js:44:7)
    at Multipart.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\lib\make-middleware.js:107:7)
    at Multipart.emit (node:events:514:28)
    at HeaderParser.cb (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\busboy\lib\types\multipart.js:358:14)
2025-06-02T07:46:58.101Z [ERROR] Error: Upload ID and chunk index are required
    at DiskStorage.filename [as getFilename] (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:28:17)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\storage\disk.js:34:10
    at DiskStorage.destination [as getDestination] (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:22:5)
    at DiskStorage._handleFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\storage\disk.js:31:8)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\lib\make-middleware.js:137:17
    at chunkFilter (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:48:3)
    at wrappedFileFilter (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\index.js:44:7)
    at Multipart.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\lib\make-middleware.js:107:7)
    at Multipart.emit (node:events:514:28)
    at HeaderParser.cb (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\busboy\lib\types\multipart.js:358:14)
2025-06-02T07:47:00.300Z [ERROR] Error: Upload ID and chunk index are required
    at DiskStorage.filename [as getFilename] (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:28:17)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\storage\disk.js:34:10
    at DiskStorage.destination [as getDestination] (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:22:5)
    at DiskStorage._handleFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\storage\disk.js:31:8)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\lib\make-middleware.js:137:17
    at chunkFilter (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:48:3)
    at wrappedFileFilter (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\index.js:44:7)
    at Multipart.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\lib\make-middleware.js:107:7)
    at Multipart.emit (node:events:514:28)
    at HeaderParser.cb (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\busboy\lib\types\multipart.js:358:14)
2025-06-02T07:47:03.485Z [ERROR] Error: Upload ID and chunk index are required
    at DiskStorage.filename [as getFilename] (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:28:17)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\storage\disk.js:34:10
    at DiskStorage.destination [as getDestination] (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:22:5)
    at DiskStorage._handleFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\storage\disk.js:31:8)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\lib\make-middleware.js:137:17
    at chunkFilter (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:48:3)
    at wrappedFileFilter (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\index.js:44:7)
    at Multipart.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\lib\make-middleware.js:107:7)
    at Multipart.emit (node:events:514:28)
    at HeaderParser.cb (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\busboy\lib\types\multipart.js:358:14)
2025-06-02T23:14:09.921Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 48bd8cb9-acbe-49cc-b881-2bef37509863
2025-06-02T23:14:13.119Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 48bd8cb9-acbe-49cc-b881-2bef37509863
2025-06-02T23:14:16.425Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 48bd8cb9-acbe-49cc-b881-2bef37509863
2025-06-02T23:14:19.595Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 48bd8cb9-acbe-49cc-b881-2bef37509863
2025-06-02T23:14:22.880Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 48bd8cb9-acbe-49cc-b881-2bef37509863
2025-06-03T06:07:29.249Z [ERROR] -----------------------------------
2025-06-03T06:07:29.252Z [ERROR] UNCAUGHT EXCEPTION: Error: listen EADDRINUSE: address already in use 0.0.0.0:7575
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at doListen (node:net:2069:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '0.0.0.0',
  port: 7575
}
2025-06-03T06:07:29.252Z [ERROR] -----------------------------------
2025-06-03T08:37:49.252Z [ERROR] Delete user error: TypeError: Cannot read properties of undefined (reading 'id')
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\admin.js:855:36
    at Layer.handle [as handle_request] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\router\route.js:149:13)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Permission.js:83:9
2025-06-03T08:38:05.390Z [ERROR] Delete user error: TypeError: Cannot read properties of undefined (reading 'id')
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\admin.js:855:36
    at Layer.handle [as handle_request] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\router\route.js:149:13)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Permission.js:83:9
2025-06-03T08:38:31.344Z [ERROR] Delete user error: TypeError: Cannot read properties of undefined (reading 'id')
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\admin.js:855:36
    at Layer.handle [as handle_request] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\router\route.js:149:13)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Permission.js:83:9
2025-06-03T08:38:58.129Z [ERROR] Delete user error: TypeError: Cannot read properties of undefined (reading 'id')
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\admin.js:855:36
    at Layer.handle [as handle_request] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\router\route.js:149:13)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Permission.js:83:9
2025-06-03T08:39:40.125Z [ERROR] Delete user error: TypeError: Cannot read properties of undefined (reading 'id')
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\admin.js:855:36
    at Layer.handle [as handle_request] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\router\route.js:149:13)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Permission.js:83:9
2025-06-03T08:43:14.105Z [ERROR] Delete user error: Error: SQLITE_ERROR: no such column: file_path
--> in Database#all('SELECT file_path FROM videos WHERE user_id = ?', [ '3e42578a-2600-4584-907f-53af5c4853c6' ], [Function (anonymous)])
    at Statement.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\User.js:283:14) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-06-03T08:43:21.851Z [ERROR] Delete user error: Error: SQLITE_ERROR: no such column: file_path
--> in Database#all('SELECT file_path FROM videos WHERE user_id = ?', [ '3e42578a-2600-4584-907f-53af5c4853c6' ], [Function (anonymous)])
    at Statement.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\User.js:283:14) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-06-03T08:44:49.309Z [ERROR] Delete user error: Error: SQLITE_ERROR: no such column: file_path
--> in Database#all('SELECT file_path FROM videos WHERE user_id = ?', [ '4f997b5a-3c6a-4d69-8e91-d39024ded347' ], [Function (anonymous)])
    at Statement.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\User.js:283:14) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-06-03T08:45:23.991Z [ERROR] Delete user error: [Error: SQLITE_ERROR: no such table: user_sessions] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-06-03T08:45:33.474Z [ERROR] Delete user error: [Error: SQLITE_ERROR: no such column: user_id] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-06-03T08:46:37.125Z [ERROR] Delete user error: [Error: SQLITE_ERROR: no such column: user_id] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-06-03T08:46:46.439Z [ERROR] Delete user error: [Error: SQLITE_ERROR: no such column: user_id] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-06-03T08:47:52.205Z [ERROR] Delete user error: [Error: SQLITE_ERROR: no such column: user_id] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-06-03T08:48:03.363Z [ERROR] Delete user error: [Error: SQLITE_ERROR: no such column: user_id] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-06-03T09:04:25.399Z [ERROR] -----------------------------------
2025-06-03T09:04:25.401Z [ERROR] UNCAUGHT EXCEPTION: Error: listen EADDRINUSE: address already in use 0.0.0.0:7575
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at doListen (node:net:2069:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '0.0.0.0',
  port: 7575
}
2025-06-03T09:04:25.402Z [ERROR] -----------------------------------
2025-06-03T22:11:37.195Z [ERROR] Video file not found: C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\lv-0-20250519133637-1748916584095-258328.mp4
2025-06-03T22:11:37.200Z [ERROR] Video file not found: C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\lv-0-20250519133637-1748916584095-258328.mp4
2025-06-03T22:39:52.928Z [ERROR] -----------------------------------
2025-06-03T22:39:52.932Z [ERROR] UNCAUGHT EXCEPTION: Error: listen EADDRINUSE: address already in use 0.0.0.0:7575
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at doListen (node:net:2069:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '0.0.0.0',
  port: 7575
}
2025-06-03T22:39:52.933Z [ERROR] -----------------------------------
2025-06-04T01:42:27.225Z [ERROR] [Performance Monitor] Error tracked: {
  message: "ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\admin\\layout.ejs'",
  stack: "Error: ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\admin\\layout.ejs'\n" +
    '    at Object.openSync (node:fs:581:18)\n' +
    '    at Object.readFileSync [as fileLoader] (node:fs:457:35)\n' +
    '    at fileLoader (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:293:18)\n' +
    '    at handleCache (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:233:16)\n' +
    '    at tryHandleCache (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:274:16)\n' +
    '    at exports.renderFile (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:491:10)\n' +
    '    at renderFile (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs-mate\\lib\\index.js:298:7)\n' +
    '    at C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs-mate\\lib\\index.js:353:7\n' +
    '    at tryHandleCache (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:280:5)\n' +
    '    at exports.renderFile (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:491:10)',
  timestamp: '2025-06-04T01:42:27.223Z'
}
2025-06-04T01:42:49.730Z [ERROR] [Performance Monitor] Error tracked: {
  message: "ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\admin\\layout.ejs'",
  stack: "Error: ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\admin\\layout.ejs'\n" +
    '    at Object.openSync (node:fs:581:18)\n' +
    '    at Object.readFileSync [as fileLoader] (node:fs:457:35)\n' +
    '    at fileLoader (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:293:18)\n' +
    '    at handleCache (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:233:16)\n' +
    '    at tryHandleCache (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:274:16)\n' +
    '    at exports.renderFile (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:491:10)\n' +
    '    at renderFile (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs-mate\\lib\\index.js:298:7)\n' +
    '    at C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs-mate\\lib\\index.js:353:7\n' +
    '    at tryHandleCache (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:280:5)\n' +
    '    at exports.renderFile (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:491:10)',
  timestamp: '2025-06-04T01:42:49.728Z'
}
2025-06-04T12:49:46.648Z [ERROR] ReferenceError: C:\Users\<USER>\OriDrive\Desktop\streamflow\views\gallery.ejs:38
    36|       </div>
    37|       <p class="text-3xl font-bold mt-2">
 >> 38|         <span id="storage-used" class="gradient-text"><%= quota.storage.current %><%= quota.storage.unit %></span><span class="text-sm text-gray-400"> / <%= quota.storage.max %><%= quota.storage.unit %></span>
    39|       </p>
    40|       <div class="w-full bg-gray-700 rounded-full h-2.5 mt-2">
    41|         <div id="storage-bar" class="bg-gradient-to-r from-purple-500 to-purple-600 h-2.5 rounded-full transition-all duration-300" style="width: <%= quota.storage.percentage %>%"></div>

quota is not defined
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\gallery.ejs":38:26)
    at gallery (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:703:17)
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:274:36)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
    at View.renderFile [as engine] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:298:7)
    at View.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\view.js:135:8)
    at tryRender (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:657:10)
    at Function.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:609:3)
    at ServerResponse.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\response.js:1049:7)
    at res.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\seoMiddleware.js:60:27)
2025-06-04T12:49:51.783Z [ERROR] ReferenceError: C:\Users\<USER>\OriDrive\Desktop\streamflow\views\gallery.ejs:38
    36|       </div>
    37|       <p class="text-3xl font-bold mt-2">
 >> 38|         <span id="storage-used" class="gradient-text"><%= quota.storage.current %><%= quota.storage.unit %></span><span class="text-sm text-gray-400"> / <%= quota.storage.max %><%= quota.storage.unit %></span>
    39|       </p>
    40|       <div class="w-full bg-gray-700 rounded-full h-2.5 mt-2">
    41|         <div id="storage-bar" class="bg-gradient-to-r from-purple-500 to-purple-600 h-2.5 rounded-full transition-all duration-300" style="width: <%= quota.storage.percentage %>%"></div>

quota is not defined
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\gallery.ejs":38:26)
    at gallery (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:703:17)
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:274:36)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
    at View.renderFile [as engine] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:298:7)
    at View.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\view.js:135:8)
    at tryRender (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:657:10)
    at Function.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:609:3)
    at ServerResponse.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\response.js:1049:7)
    at res.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\seoMiddleware.js:60:27)
2025-06-04T12:49:52.597Z [ERROR] ReferenceError: C:\Users\<USER>\OriDrive\Desktop\streamflow\views\gallery.ejs:38
    36|       </div>
    37|       <p class="text-3xl font-bold mt-2">
 >> 38|         <span id="storage-used" class="gradient-text"><%= quota.storage.current %><%= quota.storage.unit %></span><span class="text-sm text-gray-400"> / <%= quota.storage.max %><%= quota.storage.unit %></span>
    39|       </p>
    40|       <div class="w-full bg-gray-700 rounded-full h-2.5 mt-2">
    41|         <div id="storage-bar" class="bg-gradient-to-r from-purple-500 to-purple-600 h-2.5 rounded-full transition-all duration-300" style="width: <%= quota.storage.percentage %>%"></div>

quota is not defined
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\gallery.ejs":38:26)
    at gallery (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:703:17)
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:274:36)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
    at View.renderFile [as engine] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:298:7)
    at View.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\view.js:135:8)
    at tryRender (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:657:10)
    at Function.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:609:3)
    at ServerResponse.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\response.js:1049:7)
    at res.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\seoMiddleware.js:60:27)
2025-06-04T12:50:13.608Z [ERROR] ReferenceError: C:\Users\<USER>\OriDrive\Desktop\streamflow\views\gallery.ejs:38
    36|       </div>
    37|       <p class="text-3xl font-bold mt-2">
 >> 38|         <span id="storage-used" class="gradient-text"><%= quota.storage.current %><%= quota.storage.unit %></span><span class="text-sm text-gray-400"> / <%= quota.storage.max %><%= quota.storage.unit %></span>
    39|       </p>
    40|       <div class="w-full bg-gray-700 rounded-full h-2.5 mt-2">
    41|         <div id="storage-bar" class="bg-gradient-to-r from-purple-500 to-purple-600 h-2.5 rounded-full transition-all duration-300" style="width: <%= quota.storage.percentage %>%"></div>

quota is not defined
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\gallery.ejs":38:26)
    at gallery (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:703:17)
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:274:36)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
    at View.renderFile [as engine] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:298:7)
    at View.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\view.js:135:8)
    at tryRender (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:657:10)
    at Function.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:609:3)
    at ServerResponse.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\response.js:1049:7)
    at res.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\seoMiddleware.js:60:27)
2025-06-04T12:51:20.061Z [ERROR] ReferenceError: C:\Users\<USER>\OriDrive\Desktop\streamflow\views\gallery.ejs:38
    36|       </div>
    37|       <p class="text-3xl font-bold mt-2">
 >> 38|         <span id="storage-used" class="gradient-text"><%= quota.storage.current %><%= quota.storage.unit %></span><span class="text-sm text-gray-400"> / <%= quota.storage.max %><%= quota.storage.unit %></span>
    39|       </p>
    40|       <div class="w-full bg-gray-700 rounded-full h-2.5 mt-2">
    41|         <div id="storage-bar" class="bg-gradient-to-r from-purple-500 to-purple-600 h-2.5 rounded-full transition-all duration-300" style="width: <%= quota.storage.percentage %>%"></div>

quota is not defined
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\gallery.ejs":38:26)
    at gallery (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:703:17)
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:274:36)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
    at View.renderFile [as engine] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:298:7)
    at View.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\view.js:135:8)
    at tryRender (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:657:10)
    at Function.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:609:3)
    at ServerResponse.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\response.js:1049:7)
    at res.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\seoMiddleware.js:60:27)
2025-06-04T12:51:38.157Z [ERROR] -----------------------------------
2025-06-04T12:51:38.160Z [ERROR] UNCAUGHT EXCEPTION: Error: listen EADDRINUSE: address already in use 0.0.0.0:7575
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at doListen (node:net:2069:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '0.0.0.0',
  port: 7575
}
2025-06-04T12:51:38.161Z [ERROR] -----------------------------------
2025-06-04T12:54:59.403Z [ERROR] -----------------------------------
2025-06-04T12:54:59.408Z [ERROR] UNCAUGHT EXCEPTION: Error: listen EADDRINUSE: address already in use 0.0.0.0:7575
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at doListen (node:net:2069:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '0.0.0.0',
  port: 7575
}
2025-06-04T12:54:59.409Z [ERROR] -----------------------------------
2025-06-04T13:07:37.565Z [ERROR] [Performance Monitor] Error tracked: {
  message: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\admin\\users.ejs:101\n' +
    '    99|         <form method="GET" action="/admin/users" class="relative">\n' +
    `    100|           <input type="text" name="search" id="searchUsers" placeholder="<%= t('admin.search_users') %>"\n` +
    ` >> 101|                  value="<%= search || '' %>"\n` +
    '    102|                  class="bg-dark-700 border border-gray-600 text-white pl-9 pr-4 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">\n' +
    '    103|           <i class="ti ti-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>\n' +
    '    104|           <% if (search) { %>\n' +
    '\n' +
    'search is not defined',
  stack: 'ReferenceError: C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\admin\\users.ejs:101\n' +
    '    99|         <form method="GET" action="/admin/users" class="relative">\n' +
    `    100|           <input type="text" name="search" id="searchUsers" placeholder="<%= t('admin.search_users') %>"\n` +
    ` >> 101|                  value="<%= search || '' %>"\n` +
    '    102|                  class="bg-dark-700 border border-gray-600 text-white pl-9 pr-4 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">\n' +
    '    103|           <i class="ti ti-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>\n' +
    '    104|           <% if (search) { %>\n' +
    '\n' +
    'search is not defined\n' +
    '    at eval ("C:\\\\Users\\\\<USER>\\\\OriDrive\\\\Desktop\\\\streamflow\\\\views\\\\admin\\\\users.ejs":95:26)\n' +
    '    at users (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:703:17)\n' +
    '    at tryHandleCache (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:274:36)\n' +
    '    at exports.renderFile (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:491:10)\n' +
    '    at View.renderFile [as engine] (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs-mate\\lib\\index.js:298:7)\n' +
    '    at View.render (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\express\\lib\\view.js:135:8)\n' +
    '    at tryRender (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\express\\lib\\application.js:657:10)\n' +
    '    at Function.render (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\express\\lib\\application.js:609:3)\n' +
    '    at ServerResponse.render (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\express\\lib\\response.js:1049:7)\n' +
    '    at res.render (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\middleware\\seoMiddleware.js:60:27)',
  timestamp: '2025-06-04T13:07:37.560Z'
}
2025-06-04T22:35:00.011Z [ERROR] [VideoProcessing] FFmpeg error for a3354f40-dda7-4a76-a1e2-f3feeba62861: Error: ffmpeg exited with code 1: Option thread_queue_size (set the maximum number of queued packets from the demuxer) cannot be applied to output url C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\processed_df570a47-4bf1-4531-88d0-36ae3ecbf8f8.mp4 -- you are trying to apply an input option to an output file or vice versa. Move this option before the file it belongs to.
Error parsing options for output file C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\processed_df570a47-4bf1-4531-88d0-36ae3ecbf8f8.mp4.
Error opening output files: Invalid argument

    at ChildProcess.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\fluent-ffmpeg\lib\processor.js:180:22)
    at ChildProcess.emit (node:events:514:28)
    at ChildProcess._handle.onexit (node:internal/child_process:294:12)
2025-06-04T22:35:00.015Z [ERROR] [VideoProcessing] Error processing video a3354f40-dda7-4a76-a1e2-f3feeba62861: Error: Video processing failed: ffmpeg exited with code 1: Option thread_queue_size (set the maximum number of queued packets from the demuxer) cannot be applied to output url C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\processed_df570a47-4bf1-4531-88d0-36ae3ecbf8f8.mp4 -- you are trying to apply an input option to an output file or vice versa. Move this option before the file it belongs to.
Error parsing options for output file C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\processed_df570a47-4bf1-4531-88d0-36ae3ecbf8f8.mp4.
Error opening output files: Invalid argument

    at FfmpegCommand.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\videoProcessingService.js:474:20)
    at FfmpegCommand.emit (node:events:514:28)
    at emitEnd (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\fluent-ffmpeg\lib\processor.js:422:16)
    at endCB (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\fluent-ffmpeg\lib\processor.js:543:13)
    at handleExit (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\fluent-ffmpeg\lib\processor.js:168:11)
    at ChildProcess.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\fluent-ffmpeg\lib\processor.js:180:11)
    at ChildProcess.emit (node:events:514:28)
    at ChildProcess._handle.onexit (node:internal/child_process:294:12)
2025-06-04T22:36:17.233Z [ERROR] [VideoProcessing] FFmpeg error for a3354f40-dda7-4a76-a1e2-f3feeba62861: Error: ffmpeg exited with code 1: Option thread_queue_size (set the maximum number of queued packets from the demuxer) cannot be applied to output url C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\processed_7472389e-2b7c-4991-b442-997b4f088a4c.mp4 -- you are trying to apply an input option to an output file or vice versa. Move this option before the file it belongs to.
Error parsing options for output file C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\processed_7472389e-2b7c-4991-b442-997b4f088a4c.mp4.
Error opening output files: Invalid argument

    at ChildProcess.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\fluent-ffmpeg\lib\processor.js:180:22)
    at ChildProcess.emit (node:events:514:28)
    at ChildProcess._handle.onexit (node:internal/child_process:294:12)
2025-06-04T22:36:17.237Z [ERROR] [VideoProcessing] Error processing video a3354f40-dda7-4a76-a1e2-f3feeba62861: Error: Video processing failed: ffmpeg exited with code 1: Option thread_queue_size (set the maximum number of queued packets from the demuxer) cannot be applied to output url C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\processed_7472389e-2b7c-4991-b442-997b4f088a4c.mp4 -- you are trying to apply an input option to an output file or vice versa. Move this option before the file it belongs to.
Error parsing options for output file C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\processed_7472389e-2b7c-4991-b442-997b4f088a4c.mp4.
Error opening output files: Invalid argument

    at FfmpegCommand.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\videoProcessingService.js:474:20)
    at FfmpegCommand.emit (node:events:514:28)
    at emitEnd (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\fluent-ffmpeg\lib\processor.js:422:16)
    at endCB (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\fluent-ffmpeg\lib\processor.js:543:13)
    at handleExit (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\fluent-ffmpeg\lib\processor.js:168:11)
    at ChildProcess.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\fluent-ffmpeg\lib\processor.js:180:11)
    at ChildProcess.emit (node:events:514:28)
    at ChildProcess._handle.onexit (node:internal/child_process:294:12)
2025-06-04T22:45:17.653Z [ERROR] Video file not found: C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\lv-0-20250519133637-1748916584095-258328.mp4
2025-06-04T22:45:17.661Z [ERROR] Video file not found: C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\lv-0-20250519133637-1748916584095-258328.mp4
2025-06-04T22:45:18.089Z [ERROR] Video file not found: C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\lv-0-20250519133637-1748916584095-258328.mp4
2025-06-04T22:45:18.100Z [ERROR] Video file not found: C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\lv-0-20250519133637-1748916584095-258328.mp4
2025-06-04T23:50:47.510Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list:
2025-06-04T23:50:47.521Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-04T23:50:50.595Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-04T23:50:50.599Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-04T23:50:53.666Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-04T23:50:53.673Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-04T23:50:56.745Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list:
2025-06-04T23:50:56.749Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-04T23:50:59.835Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-04T23:50:59.842Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-04T23:51:35.514Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-04T23:51:35.560Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-04T23:51:38.628Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list:
2025-06-04T23:51:38.634Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-04T23:51:41.698Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-04T23:51:41.705Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-04T23:51:44.774Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-04T23:51:44.778Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-04T23:51:47.842Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-04T23:51:47.846Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-04T23:53:03.066Z [ERROR] 🚨 [ERROR] {
  "errorId": "0dbb3ef4-a8ad-424d-bb64-bf23ba1ecff7",
  "timestamp": "2025-06-04T23:53:03.064Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.",
  "statusCode": 500,
  "stack": "AppError: Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.\n    at createStreamingError (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\utils\\errorHandler.js:236:10)\n    at Object.startStream (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\services\\streamingService.js:791:13)\n    at C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\app.js:3911:45",
  "streamId": "863ae2e2-d827-416c-931d-580fe5eadb94",
  "operation": "startStream",
  "context": {
    "streamId": "863ae2e2-d827-416c-931d-580fe5eadb94",
    "operation": "start"
  }
}
2025-06-04T23:53:21.053Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-04T23:53:21.054Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-04T23:53:24.116Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-04T23:53:24.121Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-04T23:53:27.198Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-04T23:53:27.204Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-04T23:53:30.269Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list:
2025-06-04T23:53:30.276Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-04T23:53:33.346Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-04T23:53:33.350Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-04T23:59:54.265Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-04T23:59:54.275Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-04T23:59:57.348Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list:
2025-06-04T23:59:57.352Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-05T00:00:00.425Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-05T00:00:00.432Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-05T00:00:03.500Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-05T00:00:03.505Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-05T00:00:06.567Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-05T00:00:06.573Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-05T00:03:19.937Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-05T00:03:19.938Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-05T00:03:23.020Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-05T00:03:23.028Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-05T00:03:26.092Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list:
2025-06-05T00:03:26.098Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-05T00:03:29.158Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list:
2025-06-05T00:03:29.162Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-05T00:03:32.239Z [ERROR] [FFMPEG_STDERR] 863ae2e2-d827-416c-931d-580fe5eadb94: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-05T00:03:32.244Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 863ae2e2-d827-416c-931d-580fe5eadb94
2025-06-05T04:50:42.454Z [ERROR] Error adding referral_code column: Error: SQLITE_ERROR: Cannot add a UNIQUE column
--> in Database#run('ALTER TABLE users ADD COLUMN referral_code TEXT UNIQUE', [Function (anonymous)])
    at Statement.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\db\database.js:415:10) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-06-05T04:51:01.498Z [ERROR] Error getting user referral stats: [Error: SQLITE_ERROR: no such column: referral_code] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-06-05T04:51:01.499Z [ERROR] Referral dashboard error: [Error: SQLITE_ERROR: no such column: referral_code] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-06-05T04:51:04.371Z [ERROR] Error getting user referral stats: [Error: SQLITE_ERROR: no such column: referral_code] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-06-05T04:51:04.373Z [ERROR] Referral dashboard error: [Error: SQLITE_ERROR: no such column: referral_code] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-06-05T04:52:46.798Z [ERROR] [Performance Monitor] Error tracked: {
  message: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\referral\\dashboard.ejs:43\n' +
    '    41|     </div>\n' +
    '    42|     <div class="text-2xl font-bold text-white mb-1">\n' +
    ' >> 43|       <%= formatIDR(stats.balance) %>\n' +
    '    44|     </div>\n' +
    '    45|     <p class="text-gray-400 text-sm">Saldo tersedia</p>\n' +
    '    46|   </div>\n' +
    '\n' +
    'formatIDR is not defined',
  stack: 'ReferenceError: C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\referral\\dashboard.ejs:43\n' +
    '    41|     </div>\n' +
    '    42|     <div class="text-2xl font-bold text-white mb-1">\n' +
    ' >> 43|       <%= formatIDR(stats.balance) %>\n' +
    '    44|     </div>\n' +
    '    45|     <p class="text-gray-400 text-sm">Saldo tersedia</p>\n' +
    '    46|   </div>\n' +
    '\n' +
    'formatIDR is not defined\n' +
    '    at eval ("C:\\\\Users\\\\<USER>\\\\OriDrive\\\\Desktop\\\\streamflow\\\\views\\\\referral\\\\dashboard.ejs":13:7)\n' +
    '    at dashboard (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:703:17)\n' +
    '    at tryHandleCache (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:274:36)\n' +
    '    at exports.renderFile (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:491:10)\n' +
    '    at View.renderFile [as engine] (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs-mate\\lib\\index.js:298:7)\n' +
    '    at View.render (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\express\\lib\\view.js:135:8)\n' +
    '    at tryRender (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\express\\lib\\application.js:657:10)\n' +
    '    at Function.render (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\express\\lib\\application.js:609:3)\n' +
    '    at ServerResponse.render (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\express\\lib\\response.js:1049:7)\n' +
    '    at res.render (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\middleware\\seoMiddleware.js:60:27)',
  timestamp: '2025-06-05T04:52:46.783Z'
}
2025-06-05T04:52:54.243Z [ERROR] [Performance Monitor] Error tracked: {
  message: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\referral\\dashboard.ejs:43\n' +
    '    41|     </div>\n' +
    '    42|     <div class="text-2xl font-bold text-white mb-1">\n' +
    ' >> 43|       <%= formatIDR(stats.balance) %>\n' +
    '    44|     </div>\n' +
    '    45|     <p class="text-gray-400 text-sm">Saldo tersedia</p>\n' +
    '    46|   </div>\n' +
    '\n' +
    'formatIDR is not defined',
  stack: 'ReferenceError: C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\referral\\dashboard.ejs:43\n' +
    '    41|     </div>\n' +
    '    42|     <div class="text-2xl font-bold text-white mb-1">\n' +
    ' >> 43|       <%= formatIDR(stats.balance) %>\n' +
    '    44|     </div>\n' +
    '    45|     <p class="text-gray-400 text-sm">Saldo tersedia</p>\n' +
    '    46|   </div>\n' +
    '\n' +
    'formatIDR is not defined\n' +
    '    at eval ("C:\\\\Users\\\\<USER>\\\\OriDrive\\\\Desktop\\\\streamflow\\\\views\\\\referral\\\\dashboard.ejs":13:7)\n' +
    '    at dashboard (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:703:17)\n' +
    '    at tryHandleCache (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:274:36)\n' +
    '    at exports.renderFile (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs\\lib\\ejs.js:491:10)\n' +
    '    at View.renderFile [as engine] (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\ejs-mate\\lib\\index.js:298:7)\n' +
    '    at View.render (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\express\\lib\\view.js:135:8)\n' +
    '    at tryRender (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\express\\lib\\application.js:657:10)\n' +
    '    at Function.render (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\express\\lib\\application.js:609:3)\n' +
    '    at ServerResponse.render (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\express\\lib\\response.js:1049:7)\n' +
    '    at res.render (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\middleware\\seoMiddleware.js:60:27)',
  timestamp: '2025-06-05T04:52:54.238Z'
}
2025-06-05T06:03:00.506Z [ERROR] Error processing referral commission: ReferenceError: referral is not defined
    at Referral.processCommission (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Referral.js:351:88)
    at async activateSubscription (C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\payment.js:470:7)
    at async C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\payment.js:366:7
2025-06-05T06:03:00.506Z [ERROR] ❌ Error processing referral commission: ReferenceError: referral is not defined
    at Referral.processCommission (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Referral.js:351:88)
    at async activateSubscription (C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\payment.js:470:7)
    at async C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\payment.js:366:7
