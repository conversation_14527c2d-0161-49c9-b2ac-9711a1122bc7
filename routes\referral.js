const express = require('express');
const router = express.Router();
const Referral = require('../models/Referral');
const User = require('../models/User');
const {
  asyncHandler,
  createNotFoundError,
  createValidationError,
  formatErrorResponse
} = require('../utils/errorHandler');

// Middleware to check authentication
const isAuthenticated = (req, res, next) => {
  if (req.session.userId) {
    return next();
  }
  res.redirect('/login');
};

// Referral dashboard
router.get('/dashboard', isAuthenticated, asyncHandler(async (req, res) => {
  try {
    const userId = req.session.userId;
    
    // Get or create referral code for user
    let stats = await Referral.getUserReferralStats(userId);
    
    if (!stats.referral_code) {
      // Generate referral code if user doesn't have one
      const referralCode = await Referral.generateReferralCode(userId);
      stats.referral_code = referralCode;
    }

    // Get earnings history
    const earnings = await Referral.getUserEarnings(userId, 10, 0);

    // Generate referral link (use click tracking URL)
    const baseUrl = process.env.BASE_URL || 'http://localhost:7575';
    const referralLink = `${baseUrl}/referral/click/${stats.referral_code}`;

    // Format currency helper
    const formatIDR = (amount) => {
      return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0
      }).format(amount);
    };

    res.render('referral/dashboard', {
      title: 'Program Referral',
      active: 'referral',
      stats,
      earnings,
      referralLink,
      formatIDR,
      csrfToken: req.csrfToken ? req.csrfToken() : ''
    });
  } catch (error) {
    console.error('Referral dashboard error:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load referral dashboard',
      error: error
    });
  }
}));

// Get referral statistics API
router.get('/api/stats', isAuthenticated, asyncHandler(async (req, res) => {
  try {
    const userId = req.session.userId;
    const stats = await Referral.getUserReferralStats(userId);
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get referral stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get referral statistics'
    });
  }
}));

// Get earnings history API
router.get('/api/earnings', isAuthenticated, asyncHandler(async (req, res) => {
  try {
    const userId = req.session.userId;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    const earnings = await Referral.getUserEarnings(userId, limit, offset);

    res.json({
      success: true,
      data: earnings,
      pagination: {
        page,
        limit,
        hasMore: earnings.length === limit
      }
    });
  } catch (error) {
    console.error('Get earnings error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get earnings history'
    });
  }
}));

// Get detailed referral list API
router.get('/api/referral-details', isAuthenticated, asyncHandler(async (req, res) => {
  try {
    const userId = req.session.userId;
    const details = await Referral.getUserReferralDetails(userId);

    res.json({
      success: true,
      data: details
    });
  } catch (error) {
    console.error('Get referral details error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get referral details'
    });
  }
}));

// Create withdrawal request
router.post('/api/withdraw', isAuthenticated, asyncHandler(async (req, res) => {
  try {
    const userId = req.session.userId;
    const { amount, bankName, accountNumber, accountName } = req.body;

    // Validate input
    if (!amount || !bankName || !accountNumber || !accountName) {
      return res.status(400).json({
        success: false,
        error: 'All fields are required'
      });
    }

    const withdrawalAmount = parseInt(amount);
    if (isNaN(withdrawalAmount) || withdrawalAmount < 50000) {
      return res.status(400).json({
        success: false,
        error: 'Minimum withdrawal amount is Rp 50,000'
      });
    }

    // Create withdrawal request
    const withdrawal = await Referral.createWithdrawalRequest({
      userId,
      amount: withdrawalAmount,
      bankName,
      accountNumber,
      accountName
    });

    res.json({
      success: true,
      message: 'Withdrawal request submitted successfully',
      data: withdrawal
    });
  } catch (error) {
    console.error('Create withdrawal error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to create withdrawal request'
    });
  }
}));

// Track referral click (public endpoint)
router.get('/click/:code', asyncHandler(async (req, res) => {
  try {
    const { code } = req.params;
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent') || '';

    // Validate referral code
    const referral = await Referral.getReferralByCode(code);
    if (!referral) {
      return res.redirect('/register');
    }

    // Track the click
    await Referral.trackClick(code, ipAddress, userAgent);

    // Redirect to registration with referral code
    res.redirect(`/register?ref=${code}`);
  } catch (error) {
    console.error('Track referral click error:', error);
    res.redirect('/register');
  }
}));

// Generate new referral code (if needed)
router.post('/api/generate-code', isAuthenticated, asyncHandler(async (req, res) => {
  try {
    const userId = req.session.userId;
    
    // Check if user already has a referral code
    const stats = await Referral.getUserReferralStats(userId);
    if (stats.referral_code) {
      return res.json({
        success: true,
        referral_code: stats.referral_code,
        message: 'You already have a referral code'
      });
    }

    // Generate new referral code
    const referralCode = await Referral.generateReferralCode(userId);
    
    res.json({
      success: true,
      referral_code: referralCode,
      message: 'Referral code generated successfully'
    });
  } catch (error) {
    console.error('Generate referral code error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate referral code'
    });
  }
}));

// Get withdrawal requests for user
router.get('/api/withdrawals', isAuthenticated, asyncHandler(async (req, res) => {
  try {
    const userId = req.session.userId;
    
    const withdrawals = await new Promise((resolve, reject) => {
      const { db } = require('../db/database');
      db.all(
        `SELECT * FROM withdrawal_requests 
         WHERE user_id = ? 
         ORDER BY requested_at DESC`,
        [userId],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        }
      );
    });

    res.json({
      success: true,
      data: withdrawals
    });
  } catch (error) {
    console.error('Get withdrawals error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get withdrawal requests'
    });
  }
}));

// Debug endpoint to check referral data
router.get('/debug/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { db } = require('../db/database');

    // Get user info
    const user = await new Promise((resolve, reject) => {
      db.get(
        `SELECT id, username, referral_code, referred_by FROM users WHERE id = ?`,
        [userId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    // Get referral records where this user is referrer
    const asReferrer = await new Promise((resolve, reject) => {
      db.all(
        `SELECT r.*, u.username as referee_username
         FROM referrals r
         LEFT JOIN users u ON r.referee_id = u.id
         WHERE r.referrer_id = ?`,
        [userId],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        }
      );
    });

    // Get referral records where this user is referee
    const asReferee = await new Promise((resolve, reject) => {
      db.all(
        `SELECT r.*, u.username as referrer_username
         FROM referrals r
         LEFT JOIN users u ON r.referrer_id = u.id
         WHERE r.referee_id = ?`,
        [userId],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        }
      );
    });

    res.json({
      user,
      asReferrer,
      asReferee,
      stats: await Referral.getUserReferralStats(userId)
    });
  } catch (error) {
    console.error('Debug referral error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Format currency helper
router.use((req, res, next) => {
  res.locals.formatIDR = (amount) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount);
  };
  next();
});

// Testing route to update user balance (REMOVE IN PRODUCTION)
router.post('/test/update-balance', async (req, res) => {
  try {
    const { username, balance } = req.body;

    if (!username || balance === undefined) {
      return res.status(400).json({ error: 'Username and balance required' });
    }

    // Update user balance
    const result = await new Promise((resolve, reject) => {
      db.run(
        'UPDATE users SET referral_balance = ? WHERE username = ?',
        [parseInt(balance), username],
        function(err) {
          if (err) reject(err);
          else resolve({ changes: this.changes });
        }
      );
    });

    if (result.changes === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Get updated user data
    const user = await new Promise((resolve, reject) => {
      db.get(
        'SELECT username, referral_balance FROM users WHERE username = ?',
        [username],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    res.json({
      success: true,
      message: `Balance updated for ${username}`,
      user: {
        username: user.username,
        balance: user.referral_balance
      }
    });

  } catch (error) {
    console.error('Error updating balance:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
